package com.cl.project.business.controller;

import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.project.business.service.IDingtalkApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 钉钉数据同步Controller
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Api(tags = "钉钉数据同步")
@RestController
@RequestMapping("/business/dingtalk")
public class DingtalkSyncController extends BaseController
{
    @Autowired
    private IDingtalkApiService dingtalkApiService;

    /**
     * 同步钉钉用户信息到教师表
     */
    @ApiOperation("同步钉钉用户信息")
    @PostMapping("/syncUsers")
    public AjaxResult syncUsers()
    {
        try 
        {
            int syncCount = dingtalkApiService.syncAllUsers();
            return AjaxResult.success("同步完成", syncCount);
        } 
        catch (Exception e) 
        {
            logger.error("同步钉钉用户失败", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取钉钉访问令牌（测试接口）
     */
    @ApiOperation("获取钉钉访问令牌")
    @GetMapping("/getToken")
    public AjaxResult getToken()
    {
        try 
        {
            String token = dingtalkApiService.getAccessToken();
            if (token != null) 
            {
                return AjaxResult.success("获取令牌成功", token);
            } 
            else 
            {
                return AjaxResult.error("获取令牌失败");
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉令牌失败", e);
            return AjaxResult.error("获取令牌失败: " + e.getMessage());
        }
    }
}
