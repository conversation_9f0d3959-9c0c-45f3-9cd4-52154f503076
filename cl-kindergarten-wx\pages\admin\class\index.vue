<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">班级管理</text>
				</view>
			</view>
		</view>

		<!-- 新增班级按钮 -->
		<view class="add-class-section">
			<view class="add-class-btn" @click="addClass">
				<view class="add-btn-icon">
					<u-icon name="plus" color="#ffffff" size="24"></u-icon>
				</view>
				<text class="add-btn-text">新增班级</text>
			</view>
		</view>

		<!-- 班级列表 -->
		<view class="class-list">
			<view v-for="classItem in classList" :key="classItem.id" class="class-item-card">
				<view class="class-header">
					<view class="class-icon">{{ classItem.icon }}</view>
					<view class="class-basic-info">
						<text class="class-name">{{ classItem.name }}</text>
						<view class="class-meta">
							<text class="class-code">{{ classItem.code }}</text>
							<text class="age-range">{{ classItem.ageRange }}</text>
						</view>
					</view>
					<view class="capacity-indicator">
						<view class="capacity-circle" :style="{ background: getCapacityColor(classItem) }">
							<text class="capacity-text">{{ classItem.currentStudents }}/{{ classItem.maxCapacity }}</text>
						</view>
					</view>
				</view>

				<view class="class-details">
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">👨‍🏫 班主任</text>
							<text class="detail-value">{{ classItem.headTeacher }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">👩‍🏫 副班主任</text>
							<text class="detail-value">{{ classItem.assistantTeacher }}</text>
						</view>
					</view>
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">🏫 教室位置</text>
							<text class="detail-value">{{ classItem.classroom }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">📊 使用率</text>
							<text class="detail-value">{{ Math.round(classItem.currentStudents / classItem.maxCapacity * 100) }}%</text>
						</view>
					</view>
				</view>

				<view class="class-actions">
					<button class="action-btn primary" @click="viewClassStudents(classItem)">
						<text class="btn-icon">👥</text>
						<text class="btn-text">查看学生</text>
					</button>
					<button class="action-btn secondary" @click="editClass(classItem)">
						<text class="btn-icon">✏️</text>
						<text class="btn-text">编辑班级</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			classList: [
				{
					id: 1,
					name: '小班',
					code: 'XB001',
					ageRange: '3-4岁',
					headTeacher: '张老师',
					assistantTeacher: '刘老师',
					currentStudents: 25,
					maxCapacity: 30,
					classroom: '一楼101室',
					icon: '🎈'
				},
				{
					id: 2,
					name: '中班',
					code: 'ZB001',
					ageRange: '4-5岁',
					headTeacher: '李老师',
					assistantTeacher: '王老师',
					currentStudents: 28,
					maxCapacity: 30,
					classroom: '二楼201室',
					icon: '🎨'
				},
				{
					id: 3,
					name: '大班',
					code: 'DB001',
					ageRange: '5-6岁',
					headTeacher: '王老师',
					assistantTeacher: '陈老师',
					currentStudents: 22,
					maxCapacity: 25,
					classroom: '三楼301室',
					icon: '🎓'
				},
				{
					id: 4,
					name: '托管班',
					code: 'TG001',
					ageRange: '3-6岁',
					headTeacher: '赵老师',
					assistantTeacher: '孙老师',
					currentStudents: 15,
					maxCapacity: 20,
					classroom: '一楼102室',
					icon: '🌟'
				}
			]
		}
	},

	computed: {
		totalStudents() {
			return this.classList.reduce((total, classItem) => total + classItem.currentStudents, 0)
		},

		averageCapacity() {
			if (this.classList.length === 0) return 0
			const totalCapacityRate = this.classList.reduce((total, classItem) => {
				return total + (classItem.currentStudents / classItem.maxCapacity)
			}, 0)
			return Math.round((totalCapacityRate / this.classList.length) * 100)
		}
	},

	methods: {
		goBack() {
			uni.navigateBack()
		},

		addClass() {
			useRouter('/pages/admin/class/add', {}, 'navigateTo')
		},

		editClass(classItem) {
			toast(`编辑班级: ${classItem.name}`)
			// useRouter('/pages/admin/class/edit', { id: classItem.id }, 'navigateTo')
		},

		viewClassStudents(classItem) {
			toast(`查看 ${classItem.name} 的学生列表`)
			// useRouter('/pages/admin/class/students', { classId: classItem.id }, 'navigateTo')
		},

		// 根据容量使用率获取颜色
		getCapacityColor(classItem) {
			const rate = classItem.currentStudents / classItem.maxCapacity
			if (rate >= 0.9) return 'linear-gradient(135deg, #ff6b6b, #ee5a52)' // 红色 - 接近满员
			if (rate >= 0.7) return 'linear-gradient(135deg, #ffa726, #ff9800)' // 橙色 - 较满
			if (rate >= 0.5) return 'linear-gradient(135deg, #66bb6a, #4caf50)' // 绿色 - 适中
			return 'linear-gradient(135deg, #42a5f5, #2196f3)' // 蓝色 - 较空
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 统计卡片 */
.stats-section {
	margin: 30rpx;
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 新增班级按钮 */
.add-class-section {
	margin: 30rpx;
}

.add-class-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 20rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 40rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.add-btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 班级列表 */
.class-list {
	padding: 0 30rpx 40rpx;
}

.class-item-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 0;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.class-header {
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.class-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.class-basic-info {
	flex: 1;
}

.class-name {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
}

.class-meta {
	display: flex;
	gap: 16rpx;
}

.class-code {
	background: #e3f2fd;
	color: #1976d2;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.age-range {
	background: #f3e5f5;
	color: #7b1fa2;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;
}

.capacity-indicator {
	display: flex;
	align-items: center;
}

.capacity-circle {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.capacity-text {
	color: white;
	font-size: 20rpx;
	font-weight: 700;
	text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.2);
}

.class-details {
	padding: 24rpx;
	background: #fafafa;
}

.detail-row {
	display: flex;
	gap: 20rpx;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.detail-label {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 600;
}

.class-actions {
	padding: 20rpx 24rpx;
	display: flex;
	gap: 16rpx;
	background: white;
}

.action-btn {
	flex: 1;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 16rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s ease;

	&.primary {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
		}
	}

	&.secondary {
		background: #f5f5f5;
		color: #666;
		border: 1rpx solid #e0e0e0;

		&:active {
			background: #eeeeee;
		}
	}
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 22rpx;
	font-weight: 600;
}
</style>
