<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">课程管理</text>
				</view>
			</view>
		</view>

		<!-- 新增课程按钮 -->
		<view class="add-course-section">
			<view class="add-course-btn" @click="addCourse">
				<view class="add-btn-icon">
					<text class="icon-plus">+</text>
				</view>
				<text class="add-btn-text">添加课程</text>
			</view>
		</view>

		<!-- 课程列表 -->
		<view class="course-list">
			<view v-for="course in courseList" :key="course.id" class="course-item-card">
				<view class="course-icon">{{ course.icon }}</view>
				<view class="course-details">
					<text class="course-name">{{ course.name }}</text>
					<text class="course-info">类型: {{ course.type }} | 适用: {{ course.targetClass }}</text>
					<text class="course-info">时间: {{ course.schedule }}</text>
					<text class="course-info">授课教师: {{ course.teacher }}</text>
					<view class="course-stats">
						<text class="stats-item">费用: ¥{{ course.fee.toFixed(2) }}</text>
						<text class="stats-item">报名: {{ course.enrolledStudents }}/{{ course.maxStudents }}人</text>
					</view>
				</view>
				<view class="course-actions">
					<button class="action-btn students" @click="viewStudents(course)">学员</button>
					<button class="action-btn edit" @click="editCourse(course)">编辑</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			courseList: [
				{
					id: 1,
					name: '美术课',
					type: '兴趣班',
					targetClass: '全部班级',
					schedule: '周二、周四 15:00-16:00',
					teacher: '张老师',
					fee: 80.00,
					enrolledStudents: 15,
					maxStudents: 20,
					icon: '🎨'
				},
				{
					id: 2,
					name: '音乐课',
					type: '必修课',
					targetClass: '中班、大班',
					schedule: '周一、周三、周五 14:30-15:30',
					teacher: '李老师',
					fee: 0.00,
					enrolledStudents: 50,
					maxStudents: 60,
					icon: '🎵'
				},
				{
					id: 3,
					name: '体育课',
					type: '必修课',
					targetClass: '全部班级',
					schedule: '周二、周四 10:00-11:00',
					teacher: '王老师',
					fee: 0.00,
					enrolledStudents: 75,
					maxStudents: 80,
					icon: '⚽'
				},
				{
					id: 4,
					name: '英语启蒙',
					type: '选修课',
					targetClass: '大班',
					schedule: '周三、周五 16:00-17:00',
					teacher: '赵老师',
					fee: 120.00,
					enrolledStudents: 12,
					maxStudents: 15,
					icon: '📚'
				},
				{
					id: 5,
					name: '舞蹈课',
					type: '兴趣班',
					targetClass: '中班、大班',
					schedule: '周六 09:00-10:30',
					teacher: '孙老师',
					fee: 100.00,
					enrolledStudents: 18,
					maxStudents: 25,
					icon: '💃'
				}
			]
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addCourse() {
			toast('添加课程功能开发中...')
			// useRouter('/pages/admin/course/add', {}, 'navigateTo')
		},
		
		viewStudents(course) {
			toast(`查看 ${course.name} 的学员列表`)
			// useRouter('/pages/admin/course/students', { courseId: course.id }, 'navigateTo')
		},
		
		editCourse(course) {
			toast(`编辑课程: ${course.name}`)
			// useRouter('/pages/admin/course/edit', { id: course.id }, 'navigateTo')
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 新增课程按钮 */
.add-course-section {
	margin: 20rpx;
}

.add-course-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-plus {
	font-size: 24rpx;
	font-weight: bold;
	color: #ffffff;
}

.add-btn-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

.course-list {
	padding: 20rpx;
}

.course-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.course-icon {
	width: 80rpx;
	height: 80rpx;
	background: #f3e5f5;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
}

.course-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.course-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.course-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.course-stats {
	display: flex;
	gap: 20rpx;
	margin-top: 8rpx;
}

.stats-item {
	font-size: 24rpx;
	color: #4CAF50;
	font-weight: 600;
}

.course-actions {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.action-btn {
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 22rpx;
	
	&.students {
		background: #ff9800;
		color: white;
	}
	
	&.edit {
		background: #2196F3;
		color: white;
	}
}
</style>
