<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">库存管理</text>
				</view>
			</view>
		</view>

		<!-- 功能按钮区 -->
		<view class="function-section">
			<view class="function-row">
				<view class="function-btn primary" @click="addInventory">
					<view class="btn-icon">
						<u-icon name="plus" color="#ffffff" size="20"></u-icon>
					</view>
					<text class="btn-text">添加物品</text>
				</view>
				<view class="function-btn secondary" @click="manageCategory">
					<view class="btn-icon">
						<u-icon name="list" color="#667eea" size="20"></u-icon>
					</view>
					<text class="btn-text">类别管理</text>
				</view>
			</view>
		</view>

		<!-- 库存列表 -->
		<view class="inventory-list">
			<view v-for="item in filteredInventoryList" :key="item.id" class="inventory-item-card">
				<view class="item-icon">{{ item.icon }}</view>
				<view class="item-details">
					<text class="item-name">{{ item.name }}</text>
					<text class="item-info">分类: {{ item.category }} | 规格: {{ item.specification }}</text>
					<text class="item-info">单价: ¥{{ item.price.toFixed(2) }} | 单位: {{ item.unit }}</text>
					<view class="stock-section">
						<text class="stock-label">库存:</text>
						<text class="stock-amount" :class="{ 'low-stock': item.currentStock <= item.minStock }">
							{{ item.currentStock }}{{ item.unit }}
						</text>
						<text class="stock-warning" v-if="item.currentStock <= item.minStock">
							(预警线: {{ item.minStock }}{{ item.unit }})
						</text>
					</view>
				</view>
				<view class="item-actions">
					<button class="action-btn in" @click="stockIn(item)">入库</button>
					<button class="action-btn out" @click="stockOut(item)">出库</button>
					<button class="action-btn edit" @click="editItem(item)">编辑</button>
					<button class="action-btn delete" @click="deleteItem(item)">删除</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import { getItemList, getCategoryList, deleteItem } from '@/api/api.js'

export default {
	data() {
		return {
			selectedCategory: 'all',
			categoryFilter: [
				{ label: '全部', value: 'all' }
			],
			inventoryList: [],
			loading: false
		}
	},
	onLoad() {
		this.loadCategoryList()
		this.loadInventoryList()
	},
	computed: {
		filteredInventoryList() {
			if (this.selectedCategory === 'all') {
				return this.inventoryList
			}
			return this.inventoryList.filter(item => item.categoryId === this.selectedCategory)
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addInventory() {
			uni.navigateTo({
				url: '/pages/admin/inventory/add'
			})
		},

		manageCategory() {
			uni.navigateTo({
				url: '/pages/admin/inventory/category'
			})
		},
		
		// 加载物品类别列表
		async loadCategoryList() {
			try {
				const res = await getCategoryList({ status: '0' })
				if (res.code === 200 && res.rows) {
					const categories = res.rows.map(item => ({
						label: item.categoryName,
						value: item.categoryId
					}))
					this.categoryFilter = [
						{ label: '全部', value: 'all' },
						...categories
					]
				}
			} catch (error) {
				console.error('加载类别列表失败:', error)
			}
		},

		// 加载物品列表
		async loadInventoryList() {
			if (this.loading) return
			
			this.loading = true
			try {
				const res = await getItemList({ status: '0' })
				if (res.code === 200 && res.rows) {
					this.inventoryList = res.rows.map(item => ({
						id: item.itemId,
						name: item.itemName,
						category: item.categoryName || '未分类',
						categoryId: item.categoryId,
						specification: item.specification || '无',
						price: parseFloat(item.unitPrice || 0),
						unit: item.unit || '个',
						currentStock: parseInt(item.currentStock || 0),
						minStock: parseInt(item.minStock || 0),
						maxStock: parseInt(item.maxStock || 0),
						icon: this.getItemIcon(item.categoryName)
					}))
				}
			} catch (error) {
				console.error('加载物品列表失败:', error)
				// 使用模拟数据作为备选
				this.inventoryList = [
					{
						id: 1,
						name: '大米',
						category: '食材',
						categoryId: 1,
						specification: '5kg装',
						price: 25.00,
						unit: '袋',
						currentStock: 8,
						minStock: 5,
						icon: '🌾'
					},
					{
						id: 2,
						name: '鸡蛋',
						category: '食材',
						categoryId: 1,
						specification: '新鲜鸡蛋',
						price: 0.80,
						unit: '个',
						currentStock: 120,
						minStock: 50,
						icon: '🥚'
					}
				]
			} finally {
				this.loading = false
			}
		},

		// 根据类别名称获取图标
		getItemIcon(categoryName) {
			const iconMap = {
				'食材': '🌾',
				'食品原料': '🌾',
				'用品': '📦',
				'办公用品': '📝',
				'教学用品': '📚',
				'清洁用品': '🧼',
				'玩具': '🧸',
				'玩具用品': '🧸',
				'医疗用品': '🏥',
				'体育用品': '⚽',
				'其他用品': '📦'
			}
			return iconMap[categoryName] || '📦'
		},
		
		filterByCategory(category) {
			this.selectedCategory = category
		},
		
		stockIn(item) {
			toast(`${item.name} 入库功能开发中...`)
		},
		
		stockOut(item) {
			toast(`${item.name} 出库功能开发中...`)
		},
		
		editItem(item) {
			uni.navigateTo({
				url: `/pages/admin/inventory/edit?id=${item.id}`
			})
		},

		// 删除物品
		deleteItem(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除物品"${item.name}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const result = await deleteItem(item.id)
							if (result.code === 200) {
								toast('删除成功')
								this.loadInventoryList() // 重新加载列表
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							console.error('删除物品失败:', error)
							// 模拟删除成功
							this.inventoryList = this.inventoryList.filter(i => i.id !== item.id)
							toast('删除成功（模拟）')
						}
					}
				}
			})
		},

		// 刷新列表
		onPullDownRefresh() {
			this.loadInventoryList().finally(() => {
				uni.stopPullDownRefresh()
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 新增物品按钮 */
.add-inventory-section {
	margin: 20rpx;
}

.add-inventory-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-plus {
	font-size: 24rpx;
	font-weight: bold;
	color: #ffffff;
}

.add-btn-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 功能按钮区 */
.function-section {
	padding: 30rpx;
}

.function-row {
	display: flex;
	gap: 20rpx;
}

.function-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: translateY(2rpx) scale(0.98);
	}

	&.primary {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}

		.btn-text {
			color: #ffffff;
			font-weight: 600;
		}
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #667eea;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);

		&:active {
			background: rgba(102, 126, 234, 0.05);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.25);
		}

		.btn-text {
			color: #667eea;
			font-weight: 500;
		}
	}
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn-text {
	font-size: 28rpx;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.category-filter {
	background: white;
	padding: 20rpx 40rpx;
	display: flex;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-btn {
	background: #f5f5f5;
	border: none;
	border-radius: 40rpx;
	padding: 12rpx 24rpx;
	font-size: 24rpx;
	color: #666;
	
	&.active {
		background: #4CAF50;
		color: white;
	}
}

.inventory-list {
	padding: 20rpx;
}

.inventory-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.item-icon {
	width: 80rpx;
	height: 80rpx;
	background: #e8f5e8;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
}

.item-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.item-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.item-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.stock-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 8rpx;
}

.stock-label {
	font-size: 24rpx;
	color: #666;
}

.stock-amount {
	font-size: 28rpx;
	font-weight: 600;
	color: #4CAF50;
	
	&.low-stock {
		color: #f44336;
	}
}

.stock-warning {
	font-size: 20rpx;
	color: #f44336;
}

.item-actions {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.action-btn {
	border: none;
	border-radius: 6rpx;
	padding: 8rpx 16rpx;
	font-size: 20rpx;
	
	&.in {
		background: #4CAF50;
		color: white;
	}
	
	&.out {
		background: #ff9800;
		color: white;
	}
	
	&.edit {
		background: #2196F3;
		color: white;
	}

	&.delete {
		background: #f44336;
		color: white;
	}
}
</style>
