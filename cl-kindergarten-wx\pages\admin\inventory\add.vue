<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">新增物品</text>
				</view>
				<view class="nav-right" @click="handleSubmit">
					<text class="save-text">保存</text>
				</view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<view class="form-section">
				<view class="section-title">基本信息</view>
				
				<!-- 物品名称 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>物品名称</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.itemName" 
						placeholder="请输入物品名称"
						maxlength="100"
					/>
				</view>

				<!-- 物品编码 -->
				<view class="form-item">
					<view class="form-label">
						<text>物品编码</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.itemCode" 
						placeholder="请输入物品编码（可自动生成）"
						maxlength="50"
					/>
				</view>

				<!-- 物品类别 -->
				<view class="form-item" @click="showCategoryPicker">
					<view class="form-label">
						<text class="required">*</text>
						<text>物品类别</text>
					</view>
					<view class="form-picker">
						<text class="picker-text" :class="{ 'placeholder': !selectedCategory }">
							{{ selectedCategory ? selectedCategory.categoryName : '请选择物品类别' }}
						</text>
						<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
					</view>
				</view>

				<!-- 规格型号 -->
				<view class="form-item">
					<view class="form-label">
						<text>规格型号</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.specification" 
						placeholder="请输入规格型号"
						maxlength="100"
					/>
				</view>

				<!-- 计量单位 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>计量单位</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.unit" 
						placeholder="请输入计量单位（如：个、袋、盒等）"
						maxlength="20"
					/>
				</view>

				<!-- 单价 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>单价（元）</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.unitPrice" 
						placeholder="请输入单价"
						type="digit"
					/>
				</view>
			</view>

			<view class="form-section">
				<view class="section-title">库存信息</view>
				
				<!-- 当前库存 -->
				<view class="form-item">
					<view class="form-label">
						<text>当前库存</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.currentStock" 
						placeholder="请输入当前库存数量"
						type="number"
					/>
				</view>

				<!-- 最低库存 -->
				<view class="form-item">
					<view class="form-label">
						<text>最低库存</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.minStock" 
						placeholder="请输入最低库存预警值"
						type="number"
					/>
				</view>

				<!-- 最高库存 -->
				<view class="form-item">
					<view class="form-label">
						<text>最高库存</text>
					</view>
					<input 
						class="form-input" 
						v-model="formData.maxStock" 
						placeholder="请输入最高库存限制"
						type="number"
					/>
				</view>

				<!-- 状态 -->
				<view class="form-item" @click="showStatusPicker">
					<view class="form-label">
						<text>状态</text>
					</view>
					<view class="form-picker">
						<text class="picker-text">{{ statusOptions.find(item => item.value === formData.status)?.label || '正常' }}</text>
						<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
					</view>
				</view>

				<!-- 备注 -->
				<view class="form-item">
					<view class="form-label">
						<text>备注</text>
					</view>
					<textarea 
						class="form-textarea" 
						v-model="formData.remark" 
						placeholder="请输入备注信息"
						maxlength="500"
						auto-height
					/>
				</view>
			</view>
		</view>

		<!-- 类别选择器 -->
		<u-picker
			:show="showCategorySelector"
			:columns="categoryColumns"
			@confirm="onCategoryConfirm"
			@cancel="showCategorySelector = false"
		></u-picker>

		<!-- 状态选择器 -->
		<u-picker
			:show="showStatusSelector"
			:columns="[statusOptions]"
			@confirm="onStatusConfirm"
			@cancel="showStatusSelector = false"
		></u-picker>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import { addItem, getCategoryList } from '@/api/api.js'

export default {
	data() {
		return {
			formData: {
				itemCode: '',
				itemName: '',
				categoryId: null,
				specification: '',
				unit: '',
				unitPrice: '',
				minStock: 0,
				maxStock: 0,
				currentStock: 0,
				status: '0',
				remark: ''
			},
			categoryList: [],
			selectedCategory: null,
			showCategorySelector: false,
			showStatusSelector: false,
			statusOptions: [
				{ label: '正常', value: '0' },
				{ label: '停用', value: '1' }
			],
			loading: false
		}
	},
	computed: {
		categoryColumns() {
			return [this.categoryList.map(item => ({
				label: item.categoryName,
				value: item.categoryId,
				...item
			}))]
		}
	},
	onLoad() {
		this.loadCategoryList()
		this.generateItemCode()
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 加载物品类别列表
		async loadCategoryList() {
			try {
				const res = await getCategoryList({ status: '0' })
				if (res.code === 200) {
					this.categoryList = res.rows || []
				}
			} catch (error) {
				console.error('加载类别列表失败:', error)
				toast('加载类别列表失败')
			}
		},

		// 生成物品编码
		generateItemCode() {
			const now = new Date()
			const timestamp = now.getTime().toString().slice(-8)
			this.formData.itemCode = `ITEM${timestamp}`
		},

		// 显示类别选择器
		showCategoryPicker() {
			if (this.categoryList.length === 0) {
				toast('暂无可选择的类别')
				return
			}
			this.showCategorySelector = true
		},

		// 类别选择确认
		onCategoryConfirm(e) {
			const selected = e.value[0]
			this.selectedCategory = selected
			this.formData.categoryId = selected.value
			this.showCategorySelector = false
		},

		// 显示状态选择器
		showStatusPicker() {
			this.showStatusSelector = true
		},

		// 状态选择确认
		onStatusConfirm(e) {
			this.formData.status = e.value[0].value
			this.showStatusSelector = false
		},

		// 表单验证
		validateForm() {
			if (!this.formData.itemName.trim()) {
				toast('请输入物品名称')
				return false
			}
			if (!this.formData.categoryId) {
				toast('请选择物品类别')
				return false
			}
			if (!this.formData.unit.trim()) {
				toast('请输入计量单位')
				return false
			}
			if (!this.formData.unitPrice || parseFloat(this.formData.unitPrice) < 0) {
				toast('请输入正确的单价')
				return false
			}
			return true
		},

		// 提交表单
		async handleSubmit() {
			if (!this.validateForm()) {
				return
			}

			if (this.loading) {
				return
			}

			this.loading = true
			try {
				// 处理数据格式
				const submitData = {
					...this.formData,
					unitPrice: parseFloat(this.formData.unitPrice) || 0,
					minStock: parseInt(this.formData.minStock) || 0,
					maxStock: parseInt(this.formData.maxStock) || 0,
					currentStock: parseInt(this.formData.currentStock) || 0
				}

				const res = await addItem(submitData)
				if (res.code === 200) {
					toast('新增物品成功')
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					toast(res.msg || '新增物品失败')
				}
			} catch (error) {
				console.error('新增物品失败:', error)
				toast('新增物品失败')
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.save-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 表单容器 */
.form-container {
	padding: 20rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 600;
	padding: 20rpx 30rpx;
	margin: 0;
}

.form-item {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.form-label {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

.required {
	color: #ff4757;
	margin-right: 8rpx;
	font-weight: bold;
}

.form-input {
	width: 100%;
	height: 80rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333333;
	transition: all 0.3s ease;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
	}
}

.form-picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	padding: 0 20rpx;
	transition: all 0.3s ease;

	&:active {
		border-color: #667eea;
		background: #ffffff;
	}
}

.picker-text {
	font-size: 28rpx;
	color: #333333;

	&.placeholder {
		color: #999999;
	}
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	transition: all 0.3s ease;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
	}
}
</style>
