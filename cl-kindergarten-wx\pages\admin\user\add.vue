<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">新增用户</text>
				</view>
				<view class="nav-right" @click="saveUser">
					<u-icon name="checkmark" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 表单区域 -->
		<view class="form-container">
			<view class="form-section">
				<view class="section-title">基本信息</view>
				<u-form :model="userForm" ref="uForm" :rules="rules" label-width="160">
					<u-form-item label="用户名" prop="username">
						<u-input 
							v-model="userForm.username" 
							placeholder="请输入用户名"
							border
						/>
					</u-form-item>
					
					<u-form-item label="姓名" prop="name">
						<u-input 
							v-model="userForm.name" 
							placeholder="请输入真实姓名"
							border
						/>
					</u-form-item>
					
					<u-form-item label="手机号" prop="phone">
						<u-input 
							v-model="userForm.phone" 
							placeholder="请输入手机号"
							border
						/>
					</u-form-item>
					
					<u-form-item label="邮箱" prop="email">
						<u-input 
							v-model="userForm.email" 
							placeholder="请输入邮箱地址"
							border
						/>
					</u-form-item>
				</u-form>
			</view>

			<view class="form-section">
				<view class="section-title">角色权限</view>
				<u-form :model="userForm" ref="uForm2" label-width="160">
					<u-form-item label="用户角色" prop="role">
						<u-radio-group v-model="userForm.role" placement="row">
							<u-radio 
								v-for="role in roleOptions" 
								:key="role.value"
								:label="role.value"
								:name="role.value"
							>
								{{ role.label }}
							</u-radio>
						</u-radio-group>
					</u-form-item>
					
					<u-form-item label="用户状态" prop="status">
						<u-radio-group v-model="userForm.status" placement="row">
							<u-radio label="active" name="active">启用</u-radio>
							<u-radio label="inactive" name="inactive">禁用</u-radio>
						</u-radio-group>
					</u-form-item>
				</u-form>
			</view>

			<view class="form-section" v-if="userForm.role === 'teacher'">
				<view class="section-title">教师信息</view>
				<u-form :model="userForm" ref="uForm3" label-width="160">
					<u-form-item label="工号" prop="employeeNo">
						<u-input 
							v-model="userForm.employeeNo" 
							placeholder="请输入工号"
							border
						/>
					</u-form-item>
					
					<u-form-item label="职位" prop="position">
						<u-input 
							v-model="userForm.position" 
							placeholder="请输入职位"
							border
						/>
					</u-form-item>
					
					<u-form-item label="所属班级" prop="classId">
						<u-picker 
							:range="classOptions" 
							range-key="name"
							v-model="classIndex"
							@change="onClassChange"
						>
							<u-input 
								:value="selectedClassName" 
								placeholder="请选择所属班级"
								border
								readonly
							/>
						</u-picker>
					</u-form-item>
				</u-form>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<view class="save-btn" @click="saveUser">
				<u-icon name="checkmark" color="#ffffff" size="20"></u-icon>
				<text class="save-text">保存用户</text>
			</view>
		</view>
	</view>
</template>

<script>
import {toast, useRouter} from '@/utils/utils.js'

export default {
	data() {
		return {
			userForm: {
				username: '',
				name: '',
				phone: '',
				email: '',
				role: 'parent',
				status: 'active',
				employeeNo: '',
				position: '',
				classId: ''
			},
			classIndex: 0,
			roleOptions: [
				{ label: '家长', value: 'parent' },
				{ label: '教师', value: 'teacher' },
				{ label: '管理员', value: 'admin' }
			],
			classOptions: [
				{ id: '1', name: '小班A' },
				{ id: '2', name: '小班B' },
				{ id: '3', name: '中班A' },
				{ id: '4', name: '中班B' },
				{ id: '5', name: '大班A' },
				{ id: '6', name: '大班B' }
			],
			rules: {
				username: [
					{
						required: true,
						message: '请输入用户名',
						trigger: ['change', 'blur']
					}
				],
				name: [
					{
						required: true,
						message: '请输入真实姓名',
						trigger: ['change', 'blur']
					}
				],
				phone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: ['change', 'blur']
					},
					{
						pattern: /^1[3-9]\d{9}$/,
						message: '请输入正确的手机号',
						trigger: ['change', 'blur']
					}
				],
				email: [
					{
						type: 'email',
						message: '请输入正确的邮箱地址',
						trigger: ['change', 'blur']
					}
				]
			}
		}
	},
	
	computed: {
		selectedClassName() {
			if (this.userForm.classId) {
				const selectedClass = this.classOptions.find(c => c.id === this.userForm.classId)
				return selectedClass ? selectedClass.name : ''
			}
			return ''
		}
	},
	
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		onClassChange(e) {
			const index = e.detail.value
			this.classIndex = index
			this.userForm.classId = this.classOptions[index].id
		},
		
		saveUser() {
			// 验证表单
			this.$refs.uForm.validate(valid => {
				if (valid) {
					// 模拟保存用户
					console.log('保存用户信息:', this.userForm)
					toast('用户创建成功')
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					toast('请检查输入信息')
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 表单区域 */
.form-container {
	padding: 30rpx;
	padding-bottom: 200rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.section-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
	position: relative;
	
	&::before {
		content: '';
		position: absolute;
		left: 0;
		bottom: -2rpx;
		width: 60rpx;
		height: 4rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 2rpx;
	}
}

/* 保存按钮 */
.save-section {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 60rpx);
	z-index: 100;
}

.save-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	
	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
	}
}

.save-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* uView组件样式覆盖 */
::v-deep .u-form-item {
	margin-bottom: 30rpx;
	
	.u-form-item__body {
		padding: 20rpx 0;
	}
	
	.u-form-item__body__left__content__label {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
	}
}

::v-deep .u-input {
	font-size: 28rpx;
	color: #333333;
}

::v-deep .u-radio-group {
	gap: 30rpx;
}

::v-deep .u-radio__text {
	font-size: 28rpx;
	color: #333333;
}
</style>
