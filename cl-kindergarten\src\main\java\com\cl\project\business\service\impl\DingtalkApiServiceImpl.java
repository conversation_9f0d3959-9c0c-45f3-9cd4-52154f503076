package com.cl.project.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cl.common.utils.StringUtils;
import com.cl.common.utils.http.HttpUtils;
import com.cl.project.business.domain.KgTeacher;
import com.cl.project.business.domain.dto.DingtalkTokenResponse;
import com.cl.project.business.domain.dto.DingtalkUserDetailResponse;
import com.cl.project.business.domain.dto.DingtalkUserListResponse;
import com.cl.project.business.domain.dto.DingtalkDepartmentResponse;
import com.cl.project.business.domain.dto.DingtalkDepartmentRequest;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgStudentService;
import com.cl.project.business.service.IDingtalkApiService;
import com.cl.project.business.service.IKgTeacherService;
import com.cl.project.business.util.DingtalkConfigUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 钉钉API服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class DingtalkApiServiceImpl implements IDingtalkApiService 
{
    private static final Logger logger = LoggerFactory.getLogger(DingtalkApiServiceImpl.class);
    
    private static final String GET_TOKEN_URL = "https://oapi.dingtalk.com/gettoken";
    private static final String GET_USER_LIST_URL = "https://oapi.dingtalk.com/topapi/user/listid";
    private static final String GET_USER_DETAIL_URL = "https://oapi.dingtalk.com/topapi/v2/user/get";
    private static final String GET_DEPARTMENT_LIST_URL = "https://oapi.dingtalk.com/topapi/v2/department/listsub";
    private static final String CREATE_DEPARTMENT_URL = "https://oapi.dingtalk.com/topapi/v2/department/create";
    private static final String UPDATE_DEPARTMENT_URL = "https://oapi.dingtalk.com/topapi/v2/department/update";
    private static final String DELETE_DEPARTMENT_URL = "https://oapi.dingtalk.com/topapi/v2/department/delete";
    private static final String DELETE_USER_URL = "https://oapi.dingtalk.com/topapi/v2/user/delete";
    private static final String UPDATE_USER_URL = "https://oapi.dingtalk.com/topapi/v2/user/update";
    private static final String CREATE_USER_URL = "https://oapi.dingtalk.com/topapi/v2/user/create";
    
    @Autowired
    private IKgTeacherService kgTeacherService;
    
    @Autowired
    private IKgStudentService kgStudentService;
    
    @Autowired
    private com.cl.project.business.service.IKgClassService kgClassService;
    
    /**
     * 获取钉钉访问令牌
     */
    @Override
    public String getAccessToken() 
    {
        try 
        {
            String appKey = DingtalkConfigUtil.getAppKey();
            String appSecret = DingtalkConfigUtil.getAppSecret();
            
            if (StringUtils.isEmpty(appKey) || StringUtils.isEmpty(appSecret)) 
            {
                logger.error("钉钉AppKey或AppSecret未配置");
                return null;
            }
            
            String url = GET_TOKEN_URL;
            String params = "appkey=" + appKey + "&appsecret=" + appSecret;
            String response = HttpUtils.sendGet(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                DingtalkTokenResponse tokenResponse = JSON.parseObject(response, DingtalkTokenResponse.class);
                if (tokenResponse.isSuccess()) 
                {
                    return tokenResponse.getAccess_token();
                } 
                else 
                {
                    logger.error("获取钉钉token失败: {}", tokenResponse.getErrmsg());
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉token异常", e);
        }
        return null;
    }
    
    /**
     * 获取部门用户ID列表
     */
    @Override
    public List<String> getUserIdList(Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                return new ArrayList<>();
            }
            
            String url = GET_USER_LIST_URL + "?access_token=" + accessToken;
            
            // 钉钉API需要form-data格式
            String params = "dept_id=" + deptId;
            
            String response = HttpUtils.sendPost(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                DingtalkUserListResponse userListResponse = JSON.parseObject(response, DingtalkUserListResponse.class);
                if (userListResponse.isSuccess() && userListResponse.getResult() != null) 
                {
                    return userListResponse.getResult().getUserid_list();
                } 
                else 
                {
                    logger.error("获取钉钉用户列表失败: {}", userListResponse.getErrmsg());
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉用户列表异常", e);
        }
        return new ArrayList<>();
    }
    
    /**
     * 获取用户详细信息
     */
    @Override
    public DingtalkUserDetailResponse.Result getUserDetail(String userId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                return null;
            }
            
            String url = GET_USER_DETAIL_URL + "?access_token=" + accessToken;
            
            // 钉钉API需要form-data格式
            String params = "userid=" + userId;
            
            String response = HttpUtils.sendPost(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                DingtalkUserDetailResponse userDetailResponse = JSON.parseObject(response, DingtalkUserDetailResponse.class);
                if (userDetailResponse.isSuccess()) 
                {
                    return userDetailResponse.getResult();
                } 
                else 
                {
                    logger.error("获取钉钉用户详情失败: {}", userDetailResponse.getErrmsg());
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉用户详情异常", e);
        }
        return null;
    }
    
    /**
     * 同步所有用户信息到本地教师表
     */
    @Override
    public int syncAllUsers() 
    {
        int syncCount = 0;
        try 
        {
            // 获取根部门所有用户ID
            List<String> userIds = getUserIdList(1L);
            
            for (String userId : userIds) 
            {
                DingtalkUserDetailResponse.Result userDetail = getUserDetail(userId);
                if (userDetail != null) 
                {
                    // 检查是否已存在该钉钉用户
                    KgTeacher existingTeacher = kgTeacherService.selectKgTeacherByDingtalkUserId(userId);
                    
                    if (existingTeacher == null) 
                    {
                        // 创建新教师记录
                        KgTeacher teacher = new KgTeacher();
                        teacher.setDingtalkUserId(userId);
                        teacher.setTeacherName(userDetail.getName());
                        teacher.setPhone(userDetail.getMobile());
                        teacher.setPosition(userDetail.getTitle());
                        teacher.setAvatar(userDetail.getAvatar());
                        teacher.setEmail(userDetail.getEmail());
                        teacher.setJobNumber(userDetail.getJob_number());
                        
                        // 生成教师编号
                        if (StringUtils.isNotEmpty(userDetail.getJob_number())) 
                        {
                            teacher.setTeacherCode(userDetail.getJob_number());
                        } 
                        else 
                        {
                            teacher.setTeacherCode("DT" + userId);
                        }
                        
                        // 解析入职日期
                        if (StringUtils.isNotEmpty(userDetail.getHired_date())) 
                        {
                            try 
                            {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                teacher.setHireDate(sdf.parse(userDetail.getHired_date()));
                            } 
                            catch (ParseException e) 
                            {
                                logger.warn("解析入职日期失败: {}", userDetail.getHired_date());
                            }
                        }
                        
                        // 设置状态
                        teacher.setStatus(userDetail.getActive() ? "0" : "1");
                        teacher.setCreateBy("system");
                        teacher.setCreateTime(new Date());
                        
                        kgTeacherService.insertKgTeacher(teacher);
                        syncCount++;
                        logger.info("同步钉钉用户成功: {} - {}", userId, userDetail.getName());
                    } 
                    else 
                    {
                        // 更新现有教师信息
                        existingTeacher.setTeacherName(userDetail.getName());
                        existingTeacher.setPhone(userDetail.getMobile());
                        existingTeacher.setPosition(userDetail.getTitle());
                        existingTeacher.setStatus(userDetail.getActive() ? "0" : "1");
                        existingTeacher.setUpdateBy("system");
                        existingTeacher.setUpdateTime(new Date());
                        
                        kgTeacherService.updateKgTeacher(existingTeacher);
                        logger.info("更新钉钉用户成功: {} - {}", userId, userDetail.getName());
                    }
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("同步钉钉用户异常", e);
        }
        return syncCount;
    }
    
    /**
     * 获取指定部门的所有父部门列表
     */
    @Override
    public List<DingtalkDepartmentResponse.Department> getDepartmentList(Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                return new ArrayList<>();
            }
            
            String url = GET_DEPARTMENT_LIST_URL + "?access_token=" + accessToken;
            String params = "dept_id=" + deptId;
            
            String response = HttpUtils.sendPost(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("errcode") == 0) 
                {
                    // 直接解析返回的部门列表
                    return JSON.parseArray(jsonResponse.getString("result"), DingtalkDepartmentResponse.Department.class);
                } 
                else 
                {
                    logger.error("获取钉钉部门列表失败: {}", jsonResponse.getString("errmsg"));
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉部门列表异常", e);
        }
        return new ArrayList<>();
    }
    
    /**
     * 同步指定部门下的所有用户到学生表
     */
    @Override
    public int syncStudentsFromDepartment(Long deptId) 
    {
        int syncCount = 0;
        try 
        {
            // 获取部门下的所有用户ID
            List<String> userIds = getUserIdList(deptId);
            
            for (String userId : userIds) 
            {
                DingtalkUserDetailResponse.Result userDetail = getUserDetail(userId);
                if (userDetail != null) 
                {
                    // 检查是否已存在该钉钉用户
                    KgStudent existingStudent = kgStudentService.selectKgStudentByDingtalkUserId(userId);
                    
                    if (existingStudent == null) 
                    {
                        // 创建新学生记录
                        KgStudent student = new KgStudent();
                        student.setDingtalkUserId(userId);
                        student.setStudentName(userDetail.getName());
                        student.setPhone(userDetail.getMobile());
                        student.setParentPhone(userDetail.getMobile()); // 同时设置家长手机号
                        student.setAvatar(userDetail.getAvatar());
                        student.setEmail(userDetail.getEmail());
                        student.setStudentNumber(userDetail.getJob_number());
                        
                        // 生成学生编号
                        if (StringUtils.isNotEmpty(userDetail.getJob_number())) 
                        {
                            student.setStudentCode(userDetail.getJob_number());
                        } 
                        else 
                        {
                            student.setStudentCode("ST" + userId);
                        }
                        
                        // 设置学生状态
                        student.setStatus(userDetail.getActive() ? "0" : "1"); // 0在园 1退园
                        student.setCreateBy("system");
                        student.setCreateTime(new Date());
                        
                        kgStudentService.insertKgStudent(student);
                        syncCount++;
                        logger.info("同步钉钉学生成功: {} - {}", userId, userDetail.getName());
                    } 
                    else 
                    {
                        // 更新现有学生信息
                        existingStudent.setStudentName(userDetail.getName());
                        existingStudent.setPhone(userDetail.getMobile());
                        existingStudent.setParentPhone(userDetail.getMobile()); // 同时更新家长手机号
                        existingStudent.setAvatar(userDetail.getAvatar());
                        existingStudent.setEmail(userDetail.getEmail());
                        existingStudent.setStudentNumber(userDetail.getJob_number());
                        existingStudent.setStatus(userDetail.getActive() ? "0" : "1");
                        existingStudent.setUpdateBy("system");
                        existingStudent.setUpdateTime(new Date());
                        
                        kgStudentService.updateKgStudent(existingStudent);
                        logger.info("更新钉钉学生成功: {} - {}", userId, userDetail.getName());
                    }
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("同步部门学生异常", e);
        }
        return syncCount;
    }
    
    /**
     * 同步所有部门的学生信息
     */
    @Override
    public int syncAllStudents() 
    {
        int totalSyncCount = 0;
        try 
        {
            // 获取所有部门列表（从根部门开始）
            List<DingtalkDepartmentResponse.Department> departments = getDepartmentList(1L);
            
            // 注意：不同步根部门(dept_id=1)，因为根部门是老师部门，学生都在子部门中
            
            // 只同步子部门的学生
            for (DingtalkDepartmentResponse.Department dept : departments) 
            {
                if (dept.getDept_id() != null && !dept.getDept_id().equals(1L)) 
                {
                    int count = syncStudentsFromDepartment(dept.getDept_id());
                    totalSyncCount += count;
                    logger.info("部门[{}]同步学生数量: {}", dept.getName(), count);
                }
            }
            
            logger.info("总计同步学生数量: {}", totalSyncCount);
        } 
        catch (Exception e) 
        {
            logger.error("同步所有学生异常", e);
        }
        return totalSyncCount;
    }
    
    // ========================= 部门管理相关实现 =========================
    
    /**
     * 创建钉钉部门
     */
    @Override
    public Long createDepartment(DingtalkDepartmentRequest.CreateRequest request) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return null;
            }
            
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            
            // 准备请求体
            String requestBody = JSON.toJSONString(request);
            logger.info("创建部门请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = CREATE_DEPARTMENT_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("创建部门响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("创建部门响应为空");
                return null;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CreateResponse createResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CreateResponse.class);
            if (createResponse == null) 
            {
                logger.error("解析创建部门响应失败");
                return null;
            }
            
            if (createResponse.getErrcode() != 0) 
            {
                logger.error("创建部门失败: errcode={}, errmsg={}", createResponse.getErrcode(), createResponse.getErrmsg());
                return null;
            }
            
            if (createResponse.getResult() != null) 
            {
                Long deptId = createResponse.getResult().getDeptId();
                logger.info("创建部门成功: deptId={}", deptId);
                return deptId;
            }
            
            logger.error("创建部门响应结果为空");
            return null;
        } 
        catch (Exception e) 
        {
            logger.error("创建部门异常", e);
            return null;
        }
    }
    
    /**
     * 更新钉钉部门
     */
    @Override
    public boolean updateDepartment(DingtalkDepartmentRequest.UpdateRequest request) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体，根据钉钉官方文档，所有参数都在请求体中
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("dept_id", request.getDeptId());
            requestBodyMap.put("name", request.getName());
            requestBodyMap.put("parent_id", request.getParentId());
            requestBodyMap.put("order", request.getOrder());
            if (request.getAutoAddUser() != null) {
                requestBodyMap.put("auto_add_user", request.getAutoAddUser());
            }
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("更新部门请求: {}", requestBody);
            
            // 发送HTTP请求，使用sendPostJson以正确设置Content-Type
            String urlWithParams = UPDATE_DEPARTMENT_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("更新部门响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("更新部门响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析更新部门响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("更新部门失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("更新部门成功: deptId={}", request.getDeptId());
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("更新部门异常", e);
            return false;
        }
    }
    
    /**
     * 删除钉钉部门
     */
    @Override
    public boolean deleteDepartment(Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            
            // 准备请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dept_id", deptId);
            String requestBodyStr = JSON.toJSONString(requestBody);
            logger.info("删除部门请求: {}", requestBodyStr);
            
            // 发送HTTP请求
            String urlWithParams = DELETE_DEPARTMENT_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBodyStr);
            logger.info("删除部门响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("删除部门响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析删除部门响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("删除部门失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("删除部门成功: deptId={}", deptId);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("删除部门异常", e);
            return false;
        }
    }
    
    /**
     * 同步钉钉部门列表到本地班级
     */
    @Override
    public int syncDepartmentsToClasses(Long parentDeptId) 
    {
        int syncCount = 0;
        try 
        {
            Long deptId = parentDeptId != null ? parentDeptId : 1L; // 默认从根部门开始
            List<DingtalkDepartmentResponse.Department> departments = getDepartmentList(deptId);
            
            if (departments == null || departments.isEmpty()) 
            {
                logger.info("没有找到部门数据");
                return 0;
            }
            
            for (DingtalkDepartmentResponse.Department dept : departments) 
            {
                try 
                {
                    // 跳过根部门（dept_id = 1），只同步子部门
                    if (dept.getDept_id().equals(1L)) 
                    {
                        logger.info("跳过根部门: {}", dept.getName());
                        continue;
                    }
                    
                    // 检查部门是否已存在对应的班级
                    KgClass existingClass = kgClassService.selectKgClassByDingtalkDeptId(dept.getDept_id());
                    
                    if (existingClass == null) 
                    {
                        // 创建新班级
                        KgClass newClass = new KgClass();
                        newClass.setClassName(dept.getName());
                        newClass.setDingtalkDeptId(dept.getDept_id());
                        newClass.setClassType("普通班"); // 默认类型
                        newClass.setCapacity(30L); // 默认容量
                        newClass.setCurrentCount(0L);
                        newClass.setStatus("0"); // 正常状态
                        newClass.setComId(dept.getFrom_union_org()); // 使用组织ID
                        
                        int result = kgClassService.insertKgClass(newClass);
                        if (result > 0) 
                        {
                            syncCount++;
                            logger.info("同步新增班级: {} -> {}", dept.getName(), dept.getDept_id());
                        }
                    } 
                    else 
                    {
                        // 更新已存在的班级
                        existingClass.setClassName(dept.getName());
                        int result = kgClassService.updateKgClass(existingClass);
                        if (result > 0) 
                        {
                            syncCount++;
                            logger.info("同步更新班级: {} -> {}", dept.getName(), dept.getDept_id());
                        }
                    }
                } 
                catch (Exception e) 
                {
                    logger.error("同步部门失败: {}", dept.getName(), e);
                }
            }
            
            logger.info("部门同步完成，共同步 {} 个班级", syncCount);
        } 
        catch (Exception e) 
        {
            logger.error("同步部门列表异常", e);
        }
        return syncCount;
    }
    
    /**
     * 删除钉钉用户
     */
    @Override
    public boolean deleteUser(String userid) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("userid", userid);
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("删除用户请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = DELETE_USER_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("删除用户响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("删除用户响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析删除用户响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("删除用户失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("删除用户成功: userid={}", userid);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("删除用户异常", e);
            return false;
        }
    }
    
    /**
     * 更新钉钉用户信息
     */
    @Override
    public boolean updateUser(String userid, String name, String mobile, String email, String title) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("userid", userid);
            if (StringUtils.isNotEmpty(name)) {
                requestBodyMap.put("name", name);
            }
            if (StringUtils.isNotEmpty(mobile)) {
                requestBodyMap.put("mobile", mobile);
            }
            if (StringUtils.isNotEmpty(email)) {
                requestBodyMap.put("email", email);
            }
            if (StringUtils.isNotEmpty(title)) {
                requestBodyMap.put("title", title);
            }
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("更新用户请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = UPDATE_USER_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("更新用户响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("更新用户响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析更新用户响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("更新用户失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("更新用户成功: userid={}", userid);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("更新用户异常", e);
            return false;
        }
    }
    
    /**
     * 创建钉钉用户
     */
    @Override
    public boolean createUser(String userid, String name, String mobile, String email, String title, Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("userid", userid);
            requestBodyMap.put("name", name);
            
            if (StringUtils.isNotEmpty(mobile)) {
                requestBodyMap.put("mobile", mobile);
            }
            if (StringUtils.isNotEmpty(email)) {
                requestBodyMap.put("email", email);
            }
            if (StringUtils.isNotEmpty(title)) {
                requestBodyMap.put("title", title);
            }
            
            // 设置部门ID列表
            if (deptId != null) {
                requestBodyMap.put("dept_id_list", deptId.toString());
            } else {
                // 默认分配到根部门
                requestBodyMap.put("dept_id_list", "1");
            }
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("创建用户请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = CREATE_USER_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("创建用户响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("创建用户响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析创建用户响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("创建用户失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("创建用户成功: userid={}, name={}", userid, name);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("创建用户异常", e);
            return false;
        }
    }
}
