import request from '@/utils/request'

// 同步钉钉用户信息到教师表
export function syncDingtalkUsers() {
  return request({
    url: '/business/dingtalk/syncUsers',
    method: 'post'
  })
}

// 获取钉钉访问令牌（测试接口）
export function getDingtalkToken() {
  return request({
    url: '/business/dingtalk/getToken',
    method: 'get'
  })
}

// 同步钉钉学生数据到学生表
export function syncDingtalkStudents() {
  return request({
    url: '/business/dingtalk/student/syncAll',
    method: 'post'
  })
}

// 根据部门ID同步钉钉学生数据
export function syncDingtalkStudentsByDepartment(deptId) {
  return request({
    url: '/business/dingtalk/student/syncByDepartment',
    method: 'post',
    params: {
      deptId: deptId
    }
  })
}

// 获取钉钉部门列表
export function getDingtalkDepartments() {
  return request({
    url: '/business/dingtalk/student/departments',
    method: 'post'
  })
}

// 同步钉钉部门到班级表
export function syncDingtalkDepartments() {
  return request({
    url: '/business/class/dingtalk/syncDepartments',
    method: 'post'
  })
}
