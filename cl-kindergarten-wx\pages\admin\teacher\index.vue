<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师管理</text>
				</view>
			</view>
		</view>

		<!-- 新增教师按钮 -->
		<view class="add-teacher-section">
			<view class="add-teacher-btn" @click="addTeacher">
				<view class="add-btn-icon">
					<text class="icon-plus">+</text>
				</view>
				<text class="add-btn-text">添加教师</text>
			</view>
		</view>

		<!-- 教师列表 -->
		<view class="teacher-list">
			<view v-for="teacher in teacherList" :key="teacher.id" class="teacher-item-card">
				<view class="teacher-avatar">{{ teacher.gender === '男' ? '👨‍🏫' : '👩‍🏫' }}</view>
				<view class="teacher-details">
					<text class="teacher-name">{{ teacher.name }}</text>
					<text class="teacher-info">工号: {{ teacher.employeeNo }} | {{ teacher.position }}</text>
					<text class="teacher-info">联系电话: {{ teacher.phone }}</text>
					<text class="teacher-info">入职时间: {{ teacher.hireDate }}</text>
					<view class="salary-section">
						<text class="salary-label">基本工资:</text>
						<text class="salary-amount">¥{{ teacher.baseSalary.toFixed(2) }}</text>
						<text class="salary-label">课时费:</text>
						<text class="salary-amount">¥{{ teacher.hourlyRate.toFixed(2) }}/节</text>
					</view>
				</view>
				<view class="teacher-actions">
					<button class="action-btn salary" @click="viewSalaryDetails(teacher)">工资详情</button>
					<button class="action-btn edit" @click="editTeacher(teacher)">编辑</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			teacherList: [
				{
					id: 1,
					name: '张老师',
					employeeNo: 'T001',
					position: '小班班主任',
					gender: '女',
					phone: '13800138001',
					hireDate: '2023-09-01',
					baseSalary: 4500.00,
					hourlyRate: 80.00
				},
				{
					id: 2,
					name: '李老师',
					employeeNo: 'T002',
					position: '中班班主任',
					gender: '男',
					phone: '13800138002',
					hireDate: '2023-09-01',
					baseSalary: 4800.00,
					hourlyRate: 85.00
				},
				{
					id: 3,
					name: '王老师',
					employeeNo: 'T003',
					position: '大班班主任',
					gender: '女',
					phone: '13800138003',
					hireDate: '2023-09-01',
					baseSalary: 5000.00,
					hourlyRate: 90.00
				},
				{
					id: 4,
					name: '赵老师',
					employeeNo: 'T004',
					position: '副班主任',
					gender: '女',
					phone: '13800138004',
					hireDate: '2024-02-15',
					baseSalary: 3800.00,
					hourlyRate: 70.00
				},
				{
					id: 5,
					name: '孙老师',
					employeeNo: 'T005',
					position: '保育员',
					gender: '女',
					phone: '13800138005',
					hireDate: '2024-03-01',
					baseSalary: 3200.00,
					hourlyRate: 60.00
				}
			]
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addTeacher() {
			toast('添加教师功能开发中...')
			// useRouter('/pages/admin/teacher/add', {}, 'navigateTo')
		},
		
		viewSalaryDetails(teacher) {
			toast(`查看 ${teacher.name} 的工资详情`)
			// useRouter('/pages/admin/teacher/salary', { id: teacher.id }, 'navigateTo')
		},
		
		editTeacher(teacher) {
			toast(`编辑教师: ${teacher.name}`)
			// useRouter('/pages/admin/teacher/edit', { id: teacher.id }, 'navigateTo')
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 新增教师按钮 */
.add-teacher-section {
	margin: 20rpx;
}

.add-teacher-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	box-shadow: 0 6rpx 20rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-plus {
	font-size: 24rpx;
	font-weight: bold;
	color: #ffffff;
}

.add-btn-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
}

.teacher-list {
	padding: 20rpx;
}

.teacher-item-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.teacher-avatar {
	width: 80rpx;
	height: 80rpx;
	background: #fff3e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
}

.teacher-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.teacher-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.teacher-info {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
}

.salary-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 8rpx;
	flex-wrap: wrap;
}

.salary-label {
	font-size: 22rpx;
	color: #666;
}

.salary-amount {
	font-size: 24rpx;
	font-weight: 600;
	color: #4CAF50;
	margin-right: 20rpx;
}

.teacher-actions {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.action-btn {
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 22rpx;
	
	&.salary {
		background: #ff9800;
		color: white;
	}
	
	&.edit {
		background: #2196F3;
		color: white;
	}
}
</style>
